import { useAuth } from '../context/AuthContext';
import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';
import { useOutfit } from '../context/OutfitContext';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook for handling complete logout process
 * Clears all user-specific data from all contexts and localStorage
 */
export const useLogout = () => {
  const { logout } = useAuth();
  const { clearWishlist } = useWishlist();
  const { clearCart } = useCart();
  const { clearAllOutfits } = useOutfit();
  const navigate = useNavigate();

  const handleCompleteLogout = async (redirectTo = '/') => {
    try {
      // Clear all context data first
      clearWishlist();
      clearCart();
      clearAllOutfits();
      
      // Then logout from auth (this also clears localStorage)
      await logout();
      
      // Navigate to specified route
      navigate(redirectTo);
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local data and navigate
      clearWishlist();
      clearCart();
      clearAllOutfits();
      navigate(redirectTo);
    }
  };

  return { handleCompleteLogout };
};
