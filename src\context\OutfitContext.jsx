import { createContext, useContext, useState, useEffect } from 'react';
import { dataService } from '../services/dataService';
import { useAuth } from './AuthContext';

const OutfitContext = createContext();

export const useOutfit = () => {
  const context = useContext(OutfitContext);
  if (!context) {
    throw new Error('useOutfit must be used within an OutfitProvider');
  }
  return context;
};

export const OutfitProvider = ({ children }) => {
  const [savedOutfits, setSavedOutfits] = useState([]);
  const [currentOutfit, setCurrentOutfit] = useState([]);
  const [loading, setLoading] = useState(false);
  const { user, isAuthenticated } = useAuth();

  // Load saved outfits from backend when user is authenticated
  useEffect(() => {
    const loadOutfits = async () => {
      if (!isAuthenticated || !user?.id) {
        // If not authenticated, clear outfit data
        setSavedOutfits([]);
        setCurrentOutfit([]);
        return;
      }

      try {
        setLoading(true);
        const outfits = await dataService.getOutfits(user.id);
        setSavedOutfits(outfits);
      } catch (error) {
        console.error('Failed to load outfits:', error);
        // Fallback to localStorage
        const saved = localStorage.getItem('wolffoxx-outfits');
        if (saved) {
          try {
            setSavedOutfits(JSON.parse(saved));
          } catch (parseError) {
            console.error('Error parsing saved outfits:', parseError);
          }
        }
      } finally {
        setLoading(false);
      }
    };

    loadOutfits();
  }, [isAuthenticated, user?.id]);

  // Save outfits to localStorage as backup (for non-authenticated users)
  useEffect(() => {
    if (!isAuthenticated) {
      localStorage.setItem('wolffoxx-outfits', JSON.stringify(savedOutfits));
    }
  }, [savedOutfits, isAuthenticated]);

  const saveOutfit = async (outfit, name = '') => {
    const outfitData = {
      name: name || `Outfit ${savedOutfits.length + 1}`,
      description: `Custom outfit with ${outfit.length} items`,
      occasion: 'casual',
      season: 'all',
      is_public: false,
      items: outfit.map(item => ({
        product_id: item.id,
        selected_color: item.selectedColor || item.color,
        selected_size: item.selectedSize || item.size,
        selected_color_hex: item.colorHex || null,
        category_type: getCategoryType(item.category),
        is_primary: false
      }))
    };

    if (isAuthenticated && user?.id) {
      try {
        const newOutfit = await dataService.createOutfit(user.id, outfitData);
        setSavedOutfits(prev => [...prev, newOutfit]);
        return newOutfit;
      } catch (error) {
        console.error('Failed to save outfit:', error);
        // Fallback to localStorage behavior
        const localOutfit = {
          id: Date.now(),
          name: outfitData.name,
          items: outfit,
          createdAt: new Date().toISOString(),
          totalPrice: outfit.reduce((total, item) => total + (item.salePrice || item.price), 0)
        };
        setSavedOutfits(prev => [...prev, localOutfit]);
        return localOutfit;
      }
    } else {
      // Not authenticated, use localStorage
      const localOutfit = {
        id: Date.now(),
        name: outfitData.name,
        items: outfit,
        createdAt: new Date().toISOString(),
        totalPrice: outfit.reduce((total, item) => total + (item.salePrice || item.price), 0)
      };
      setSavedOutfits(prev => [...prev, localOutfit]);
      return localOutfit;
    }
  };

  const deleteOutfit = async (outfitId) => {
    if (isAuthenticated && user?.id) {
      try {
        await dataService.deleteOutfit(user.id, outfitId);
        setSavedOutfits(prev => prev.filter(outfit => outfit.id !== outfitId));
      } catch (error) {
        console.error('Failed to delete outfit:', error);
        // Fallback to localStorage behavior
        setSavedOutfits(prev => prev.filter(outfit => outfit.id !== outfitId));
      }
    } else {
      // Not authenticated, use localStorage
      setSavedOutfits(prev => prev.filter(outfit => outfit.id !== outfitId));
    }
  };

  const updateOutfit = async (outfitId, updatedOutfit) => {
    if (isAuthenticated && user?.id) {
      try {
        const updated = await dataService.updateOutfit(user.id, outfitId, updatedOutfit);
        setSavedOutfits(prev =>
          prev.map(outfit =>
            outfit.id === outfitId
              ? { ...outfit, ...updated }
              : outfit
          )
        );
      } catch (error) {
        console.error('Failed to update outfit:', error);
        // Fallback to localStorage behavior
        setSavedOutfits(prev =>
          prev.map(outfit =>
            outfit.id === outfitId
              ? { ...outfit, ...updatedOutfit }
              : outfit
          )
        );
      }
    } else {
      // Not authenticated, use localStorage
      setSavedOutfits(prev =>
        prev.map(outfit =>
          outfit.id === outfitId
            ? { ...outfit, ...updatedOutfit }
            : outfit
        )
      );
    }
  };

  // Helper function to map category to category type
  const getCategoryType = (category) => {
    const categoryMap = {
      'oversized-tees': 'top',
      't-shirts': 'top',
      'shirts': 'top',
      'hoodies': 'outerwear',
      'jeans': 'bottom',
      'pants': 'bottom',
      'shorts': 'bottom',
      'shoes': 'footwear',
      'accessories': 'accessory'
    };
    return categoryMap[category] || 'top';
  };

  const addToCurrentOutfit = (product) => {
    setCurrentOutfit(prev => {
      const isAlreadyInOutfit = prev.some(item => item.id === product.id);
      if (!isAlreadyInOutfit && prev.length < 10) { // Increased limit for custom outfit builder
        return [...prev, {
          ...product,
          selectedColor: product.selectedColor || product.colors?.[0] || null,
          selectedSize: product.selectedSize || product.sizes?.[0] || 'M',
          addedAt: Date.now(),
          outfitType: 'custom' // Mark as custom outfit item
        }];
      }
      return prev;
    });
  };

  const removeFromCurrentOutfit = (productId) => {
    setCurrentOutfit(prev => prev.filter(item => item.id !== productId));
  };

  const clearCurrentOutfit = () => {
    setCurrentOutfit([]);
  };

  const clearAllOutfits = () => {
    setCurrentOutfit([]);
    setSavedOutfits([]);
    // Also clear localStorage backup
    localStorage.removeItem('wolffoxx-outfits');
    localStorage.removeItem('savedOutfits'); // Old key
    localStorage.removeItem('currentOutfit');
  };

  const getOutfitsByCategory = (category) => {
    return savedOutfits.filter(outfit =>
      outfit.items.some(item => item.category === category)
    );
  };

  const getRecentOutfits = (limit = 5) => {
    return savedOutfits
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);
  };

  const value = {
    // State
    savedOutfits,
    currentOutfit,

    // Actions
    saveOutfit,
    deleteOutfit,
    updateOutfit,
    addToCurrentOutfit,
    removeFromCurrentOutfit,
    clearCurrentOutfit,
    clearAllOutfits,

    // Getters
    getOutfitsByCategory,
    getRecentOutfits,

    // Computed values
    totalSavedOutfits: savedOutfits.length,
    currentOutfitTotal: currentOutfit.reduce((total, item) => total + (item.salePrice || item.price), 0),
    currentOutfitCount: currentOutfit.length
  };

  return (
    <OutfitContext.Provider value={value}>
      {children}
    </OutfitContext.Provider>
  );
};

export default OutfitContext;
