<?php

namespace Wolffoxx\Controllers;

use Wolffoxx\Models\Outfit;
use Wolffoxx\Models\Product;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Validator;

/**
 * Outfit Controller
 * 
 * Handles outfit management operations including
 * creating, retrieving, updating, and deleting outfits.
 */
class OutfitController extends BaseController
{
    private Outfit $outfitModel;
    private Product $productModel;

    public function __construct()
    {
        $this->outfitModel = new Outfit();
        $this->productModel = new Product();
    }

    /**
     * Get user's outfits
     * GET /api/v1/outfits
     */
    public function index(array $params = []): void
    {
        try {
            $userId = 3; // Default test user

            // Simple query to get outfits for user
            $sql = "SELECT * FROM outfits WHERE user_id = ? ORDER BY created_at DESC";
            $stmt = \Wolffoxx\Config\Database::execute($sql, [$userId]);
            $outfits = $stmt->fetchAll();

            Response::success([
                'outfits' => $outfits,
                'pagination' => [
                    'total' => count($outfits),
                    'limit' => 20,
                    'offset' => 0,
                    'has_more' => false
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get outfits failed: ' . $e->getMessage());
            Response::error('Failed to retrieve outfits');
        }
    }

    /**
     * Create new outfit
     * POST /api/v1/outfits
     */
    public function create(array $params = []): void
    {
        try {
            $userId = 3; // Default test user (same as wishlist)

            $input = $this->getJsonInput();

            // Validate input (simplified validation for now)
            $validator = new Validator($input, [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'occasion' => 'nullable|string|max:100',
                'season' => 'nullable|string|max:20',
                'style' => 'nullable|string|max:100',
                'color_scheme' => 'nullable|string|max:100',
                'is_public' => 'nullable|boolean',
                'tags' => 'nullable|string|max:500',
                'items' => 'nullable|array'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Simple direct database insert for now
            $uuid = sprintf(
                '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000,
                mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
            );

            $sql = "INSERT INTO outfits (uuid, user_id, name, description, occasion, season, style, color_scheme, is_public, tags, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            $params = [
                $uuid,
                $userId,
                $data['name'],
                $data['description'] ?? null,
                $data['occasion'] ?? null,
                $data['season'] ?? 'all',
                $data['style'] ?? null,
                $data['color_scheme'] ?? null,
                $data['is_public'] ?? false,
                $data['tags'] ?? null
            ];

            $stmt = \Wolffoxx\Config\Database::execute($sql, $params);
            $outfitId = \Wolffoxx\Config\Database::getConnection()->lastInsertId();

            if (!$outfitId) {
                Response::error('Failed to create outfit');
                return;
            }

            // Get the created outfit
            $getOutfitSql = "SELECT * FROM outfits WHERE id = ?";
            $outfitStmt = \Wolffoxx\Config\Database::execute($getOutfitSql, [$outfitId]);
            $outfit = $outfitStmt->fetch();

            Response::success([
                'outfit' => $outfit,
                'message' => 'Outfit created successfully'
            ], 201);

        } catch (\Exception $e) {
            error_log('Create outfit failed: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            Response::error('Failed to create outfit: ' . $e->getMessage());
        }
    }

    /**
     * Get single outfit with items
     * GET /api/v1/outfits/{id}
     */
    public function show(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            if (!$outfit) {
                Response::notFound('Outfit not found');
                return;
            }

            Response::success($outfit);

        } catch (\Exception $e) {
            Response::error('Failed to retrieve outfit');
        }
    }

    /**
     * Update outfit
     * PUT /api/v1/outfits/{id}
     */
    public function update(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:1000',
                'occasion' => 'nullable|string|max:100',
                'season' => 'nullable|string|max:20',
                'style' => 'nullable|string|max:100',
                'color_scheme' => 'nullable|string|max:100',
                'is_public' => 'nullable|boolean',
                'tags' => 'nullable|string|max:500'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $outfit = $this->outfitModel->update($outfitId, $data);

            if (!$outfit) {
                Response::error('Failed to update outfit');
                return;
            }

            Response::success([
                'outfit' => $outfit,
                'message' => 'Outfit updated successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to update outfit');
        }
    }

    /**
     * Delete outfit
     * DELETE /api/v1/outfits/{id}
     */
    public function delete(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $deleted = $this->outfitModel->deleteOutfit($outfitId);

            if (!$deleted) {
                Response::error('Failed to delete outfit');
                return;
            }

            Response::success([
                'message' => 'Outfit deleted successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to delete outfit');
        }
    }

    /**
     * Add item to outfit
     * POST /api/v1/outfits/{id}/items
     */
    public function addItem(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'product_id' => 'required|integer',
                'selected_color' => 'nullable|string|max:100',
                'selected_size' => 'nullable|string|max:20',
                'selected_color_hex' => 'nullable|string|max:7',
                'category_type' => 'nullable|string|max:50',
                'is_primary' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $result = $this->outfitModel->addItemToOutfit($outfitId, $data);

            if (!$result) {
                Response::error('Failed to add item to outfit');
                return;
            }

            // Get updated outfit
            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            Response::success([
                'outfit' => $outfit,
                'message' => 'Item added to outfit successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to add item to outfit');
        }
    }

    /**
     * Remove item from outfit
     * DELETE /api/v1/outfits/{id}/items/{itemId}
     */
    public function removeItem(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            $itemId = (int)$params['itemId'];

            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $result = $this->outfitModel->removeItemFromOutfit($outfitId, $itemId);

            if (!$result) {
                Response::error('Failed to remove item from outfit');
                return;
            }

            // Get updated outfit
            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            Response::success([
                'outfit' => $outfit,
                'message' => 'Item removed from outfit successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to remove item from outfit');
        }
    }

    /**
     * Get outfit collections (placeholder for future implementation)
     * GET /api/v1/outfits/collections
     */
    public function getCollections(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // For now, return empty collections
            // This can be expanded later when collections feature is needed
            Response::success([
                'collections' => [],
                'message' => 'Collections feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to retrieve collections');
        }
    }

    /**
     * Create outfit collection (placeholder for future implementation)
     * POST /api/v1/outfits/collections
     */
    public function createCollection(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Collections feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to create collection');
        }
    }

    /**
     * Toggle like on outfit (placeholder for future implementation)
     * POST /api/v1/outfits/{id}/like
     */
    public function toggleLike(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Like feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to toggle like');
        }
    }

    /**
     * Share outfit (placeholder for future implementation)
     * POST /api/v1/outfits/{id}/share
     */
    public function shareOutfit(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Share feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to share outfit');
        }
    }
}
