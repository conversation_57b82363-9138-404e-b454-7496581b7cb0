import { createContext, useContext, useState, useEffect } from 'react';
import { dataService } from '../services/dataService';
import { useAuth } from './AuthContext';

// Define types as comments since JSX doesn't support TypeScript

// type WishlistItem = {
//   id: number;
//   name: string;
//   price: number;
//   image: string;
// }

// type WishlistContextType = {
//   wishlistItems: WishlistItem[];
//   addToWishlist: (item: WishlistItem) => void;
//   removeFromWishlist: (id: number) => void;
//   isInWishlist: (id: number) => boolean;
//   totalWishlistItems: number;
// }

const WishlistContext = createContext(undefined);

export { WishlistContext };

export function WishlistProvider({ children }) {
  const [wishlistItems, setWishlistItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const { user, isAuthenticated } = useAuth();

  // Load wishlist from backend when user is authenticated
  useEffect(() => {
    const loadWishlist = async () => {
      // If user changed, clear previous user's data
      if (currentUserId && user?.id && currentUserId !== user.id) {
        setWishlistItems([]);
      }

      // Update current user ID
      setCurrentUserId(user?.id || null);

      if (!isAuthenticated || !user?.id) {
        // If not authenticated, clear wishlist data
        setWishlistItems([]);
        return;
      }

      try {
        setLoading(true);
        const wishlist = await dataService.getWishlist(user.id);

        // Transform backend wishlist items to frontend format
        const transformedItems = (wishlist || []).map(item => ({
          id: item.product_id || item.id,
          name: item.product_name || item.name,
          price: parseFloat(item.price || 0),
          salePrice: item.sale_price ? parseFloat(item.sale_price) : null,
          image: item.product_image || item.primary_image || (item.images && item.images[0]),
          category: item.category || 'general',
          colors: item.colors || [],
          sizes: item.sizes || [],
          images: item.images || [],
          isNew: item.isNew || false,
          isBestSeller: item.isBestSeller || false,
          isTrending: item.isTrending || false,
          isOnSale: item.isOnSale || false,
          rating: item.rating || 4.5,
          material: item.material || '100% Cotton',
          fit: item.fit || 'Regular',
          sku: item.sku || '',
          in_stock: item.in_stock || false,
          stock_quantity: item.stock_quantity || 0,
          wishlistItemId: item.id, // Backend wishlist item ID for removal
          notes: item.notes || null,
          priority: item.priority || 'medium',
          added_at: item.created_at || item.added_at
        }));

        setWishlistItems(transformedItems);
      } catch (error) {
        console.error('Failed to load wishlist:', error);
        // Fallback to localStorage
        const savedWishlist = localStorage.getItem('wishlist');
        if (savedWishlist) {
          setWishlistItems(JSON.parse(savedWishlist));
        }
      } finally {
        setLoading(false);
      }
    };

    loadWishlist();
  }, [isAuthenticated, user?.id]);

  // Note: Removed localStorage backup to prevent data leakage between users

  const addToWishlist = async (product) => {
    if (isAuthenticated && user?.id) {
      try {
        await dataService.addToWishlist(user.id, product.id);
        setWishlistItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex(item => item.id === product.id);
          if (existingItemIndex > -1) {
            return prevItems; // Already exists
          }
          return [...prevItems, product];
        });
      } catch (error) {
        console.error('Failed to add to wishlist:', error);
        // Fallback to localStorage behavior
        setWishlistItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex(item => item.id === product.id);
          if (existingItemIndex > -1) {
            return prevItems;
          }
          return [...prevItems, product];
        });
      }
    } else {
      // Not authenticated, use localStorage
      setWishlistItems((prevItems) => {
        const existingItemIndex = prevItems.findIndex(item => item.id === product.id);
        if (existingItemIndex > -1) {
          return prevItems;
        }
        return [...prevItems, product];
      });
    }
  };

  const removeFromWishlist = async (id) => {
    if (isAuthenticated && user?.id) {
      try {
        // Find the item to get its backend wishlist item ID
        const item = wishlistItems.find(item => item.id === id);
        const itemIdToRemove = item?.wishlistItemId || id;

        await dataService.removeFromWishlist(user.id, itemIdToRemove);
        setWishlistItems((prevItems) => prevItems.filter(item => item.id !== id));
      } catch (error) {
        console.error('Failed to remove from wishlist:', error);
        // Fallback to localStorage behavior
        setWishlistItems((prevItems) => prevItems.filter(item => item.id !== id));
      }
    } else {
      // Not authenticated, use localStorage
      setWishlistItems((prevItems) => prevItems.filter(item => item.id !== id));
    }
  };

  const isInWishlist = (id) => {
    return wishlistItems.some((item) => item.id === id);
  };

  const clearWishlist = () => {
    setWishlistItems([]);
    // Also clear localStorage backup
    localStorage.removeItem('wishlist');
  };

  const totalWishlistItems = wishlistItems.length;

  return (
    <WishlistContext.Provider
      value={{
        wishlistItems,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        clearWishlist,
        totalWishlistItems,
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
}

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};