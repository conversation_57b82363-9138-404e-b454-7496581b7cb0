import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { X } from 'lucide-react';
import OTPLogin from '../components/auth/OTPLogin';
import { useAuth } from '../context/AuthContext';
import drawingLogo from '../assets/logo45.svg';

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // Handle successful login
  const handleLoginSuccess = (data) => {
    const from = location.state?.from?.pathname || '/';
    
    // Show success message briefly
    setTimeout(() => {
      navigate(from, { replace: true });
    }, 500);
  };

  // Handle close/back
  const handleClose = () => {
    navigate(-1);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Subtle Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        <motion.div 
          className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-gray-500/3 to-gray-600/2 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.2, 0.3, 0.2]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-gray-600/2 to-gray-500/3 rounded-full blur-3xl"
          animate={{
            scale: [1.05, 1, 1.05],
            opacity: [0.3, 0.1, 0.3]
          }}
          transition={{
            duration: 14,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="relative z-10 flex items-center justify-between p-6 md:p-8 border-b border-gray-800/30"
      >
        <div className="flex items-center gap-4">
          <div className="hidden sm:block">
            <motion.h1 
              className="text-white font-semibold text-lg"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              Welcome Back
            </motion.h1>
            <motion.p 
              className="text-gray-400 text-sm mt-1"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              Sign in to your account
            </motion.p>
          </div>
        </div>
        
        <motion.button
          onClick={handleClose}
          className="p-3 text-gray-500 hover:text-white transition-all duration-300 rounded-xl hover:bg-gray-800/50 group"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <X size={20} className="group-hover:rotate-45 transition-transform duration-300" />
        </motion.button>
      </motion.header>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex items-center justify-center p-6 md:p-8">
        <motion.div 
          className="w-full max-w-lg"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Login Form */}
          <motion.div
            variants={itemVariants}
            className="relative bg-gradient-to-br from-gray-900/60 to-gray-950/40 backdrop-blur-xl border border-gray-700/30 rounded-2xl p-8 md:p-10 shadow-2xl shadow-black/10"
          >
            <div className="relative">
              <OTPLogin
                onSuccess={handleLoginSuccess}
                onClose={handleClose}
              />
            </div>
          </motion.div>

          {/* Terms */}
          <motion.div
            variants={itemVariants}
            className="mt-8 text-center"
          >
            <div className="p-4 bg-gray-900/30 rounded-xl border border-gray-700/20">
              <p className="text-gray-500 text-xs leading-relaxed">
                By continuing, you agree to our{' '}
                <motion.button 
                  className="text-orange-400 hover:text-orange-300 underline underline-offset-2 transition-colors"
                  whileHover={{ scale: 1.02 }}
                >
                  Terms of Service
                </motion.button>{' '}
                and{' '}
                <motion.button 
                  className="text-orange-400 hover:text-orange-300 underline underline-offset-2 transition-colors"
                  whileHover={{ scale: 1.02 }}
                >
                  Privacy Policy
                </motion.button>
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default LoginPage;