// Data Service - Handles transition from static data to API data
import { productAPI } from './productAPI.js';
import { products as staticProducts } from '../data/products_extended.js';

// Configuration - ENABLE BACKEND INTEGRATION
const USE_API = true; // ✅ ENABLED - Full backend integration active!

// Helper function to get auth token from AuthContext format
const getAuthToken = () => {
  try {
    const tokens = localStorage.getItem('wolffoxx_tokens');
    if (tokens) {
      const parsedTokens = JSON.parse(tokens);
      return parsedTokens.accessToken;
    }
    return null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Data transformation utilities - Updated for YOUR actual API structure
const transformProductFromAPI = (apiProduct) => {
  return {
    // Basic product info (matches your API exactly)
    id: apiProduct.id,
    name: apiProduct.name,
    category: apiProduct.category, // Your API returns this directly
    price: parseFloat(apiProduct.price),
    salePrice: apiProduct.salePrice ? parseFloat(apiProduct.salePrice) : null,
    description: apiProduct.description,

    // Rich variant data (your API provides this)
    colors: apiProduct.colors || [],
    sizes: apiProduct.sizes || [],
    images: apiProduct.images || [],

    // Status flags (your API structure)
    isNew: apiProduct.isNew || false,
    isBestSeller: apiProduct.isBestSeller || false,
    isTrending: apiProduct.isTrending || false,
    isOnSale: apiProduct.isOnSale || false,

    // Additional metadata
    rating: parseFloat(apiProduct.rating || 4.5),
    material: apiProduct.material || '100% Cotton',
    fit: apiProduct.fit || 'Regular',
    sku: apiProduct.sku || '',

    // Stock information
    in_stock: apiProduct.in_stock || false,
    stock_quantity: apiProduct.stock_quantity || 0
  };
};

const transformCategoryFromAPI = (apiCategory) => {
  return {
    id: apiCategory.id,
    name: apiCategory.name,
    slug: apiCategory.slug,
    description: apiCategory.description,
    image_url: apiCategory.image_url,
    product_count: apiCategory.product_count || 0,
    is_active: apiCategory.is_active || true
  };
};

// Data Service
export const dataService = {
  /**
   * Get all products with filtering and pagination
   */
  async getProducts(filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      // Return static data with basic filtering
      let filteredProducts = [...staticProducts];
      
      // Apply basic filters
      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => 
          p.category.toLowerCase().includes(filters.category.toLowerCase())
        );
      }
      
      if (filters.search) {
        filteredProducts = filteredProducts.filter(p => 
          p.name.toLowerCase().includes(filters.search.toLowerCase()) ||
          p.description.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      if (filters.new) {
        filteredProducts = filteredProducts.filter(p => p.isNew);
      }

      if (filters.bestseller) {
        filteredProducts = filteredProducts.filter(p => p.isBestSeller);
      }

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = filteredProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: filteredProducts.length,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage),
          has_next: end < filteredProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.getProducts(filters, page, perPage);
      // Your backend already returns the correct format, just transform if needed
      return {
        products: response.data ? response.data.map(transformProductFromAPI) : response.products.map(transformProductFromAPI),
        total: response.pagination?.total || response.total,
        pagination: response.pagination
      };
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      // Fallback to static data
      return this.getProducts(filters, page, perPage);
    }
  },

  /**
   * Get single product by ID
   */
  async getProduct(productId) {
    if (!USE_API) {
      const product = staticProducts.find(p => p.id === parseInt(productId));
      if (!product) {
        throw new Error('Product not found');
      }
      return product;
    }

    try {
      const apiProduct = await productAPI.getProduct(productId);
      return transformProductFromAPI(apiProduct);
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      const product = staticProducts.find(p => p.id === parseInt(productId));
      if (!product) {
        throw new Error('Product not found');
      }
      return product;
    }
  },

  /**
   * Get products by category
   */
  async getProductsByCategory(category, filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      const filteredProducts = staticProducts.filter(p => 
        p.category.toLowerCase().includes(category.toLowerCase())
      );

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = filteredProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: filteredProducts.length,
        category: category,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage),
          has_next: end < filteredProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.getProductsByCategory(category, filters, page, perPage);
      return {
        products: response.products.map(transformProductFromAPI),
        total: response.total,
        category: response.category,
        pagination: response.pagination
      };
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return this.getProductsByCategory(category, filters, page, perPage);
    }
  },

  /**
   * Search products
   */
  async searchProducts(query, filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      const filteredProducts = staticProducts.filter(p => 
        p.name.toLowerCase().includes(query.toLowerCase()) ||
        p.description.toLowerCase().includes(query.toLowerCase()) ||
        p.category.toLowerCase().includes(query.toLowerCase())
      );

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = filteredProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: filteredProducts.length,
        query: query,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage),
          has_next: end < filteredProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.searchProducts(query, filters, page, perPage);
      return {
        products: response.products.map(transformProductFromAPI),
        total: response.total,
        query: response.query,
        pagination: response.pagination
      };
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return this.searchProducts(query, filters, page, perPage);
    }
  },

  /**
   * Get categories
   */
  async getCategories() {
    if (!USE_API) {
      // Extract unique categories from static products
      const categories = [...new Set(staticProducts.map(p => p.category))].map((name, index) => ({
        id: index + 1,
        name: name,
        slug: name.toLowerCase().replace(/\s+/g, '-'),
        description: `${name} collection`,
        product_count: staticProducts.filter(p => p.category === name).length,
        is_active: true
      }));
      return categories;
    }

    try {
      const apiCategories = await productAPI.getCategories();
      return apiCategories.map(transformCategoryFromAPI);
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return this.getCategories();
    }
  },

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit = 12) {
    if (!USE_API) {
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }

    try {
      const apiProducts = await productAPI.getFeaturedProducts(limit);
      return apiProducts.map(transformProductFromAPI);
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }
  },

  /**
   * Get new products
   */
  async getNewProducts(limit = 12) {
    if (!USE_API) {
      return staticProducts.filter(p => p.isNew).slice(0, limit);
    }

    try {
      const apiProducts = await productAPI.getNewProducts(limit);
      return apiProducts.map(transformProductFromAPI);
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return staticProducts.filter(p => p.isNew).slice(0, limit);
    }
  },

  /**
   * Get bestseller products
   */
  async getBestsellerProducts(limit = 12) {
    if (!USE_API) {
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }

    try {
      const apiProducts = await productAPI.getBestsellerProducts(limit);
      return apiProducts.map(transformProductFromAPI);
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }
  },

  /**
   * Get sale products
   */
  async getSaleProducts(filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      const saleProducts = staticProducts.filter(p => p.salePrice);

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = saleProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: saleProducts.length,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(saleProducts.length / perPage),
          has_next: end < saleProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.getSaleProducts(filters, page, perPage);
      return {
        products: response.products.map(transformProductFromAPI),
        total: response.total,
        pagination: response.pagination
      };
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      return this.getSaleProducts(filters, page, perPage);
    }
  },

  /**
   * Get similar products (for "You Might Also Like" section)
   */
  async getSimilarProducts(currentProductId, category, limit = 8) {
    if (!USE_API) {
      // Find similar products based on category, excluding current product
      const similar = staticProducts
        .filter(p => p.id !== parseInt(currentProductId) && p.category === category)
        .slice(0, limit);

      // If not enough products in same category, add some more
      if (similar.length < limit) {
        const additional = staticProducts
          .filter(p => p.id !== parseInt(currentProductId) && !similar.find(s => s.id === p.id))
          .slice(0, limit - similar.length);

        return [...similar, ...additional];
      }

      return similar;
    }

    try {
      // Use category-based filtering to get similar products
      const response = await this.getProductsByCategory(category, {}, 1, limit + 5); // Get a few extra

      // Filter out current product and limit results
      const similarProducts = response.products
        .filter(p => p.id !== parseInt(currentProductId))
        .slice(0, limit);

      // If not enough products in same category, get some bestsellers
      if (similarProducts.length < limit) {
        const bestsellers = await this.getBestsellerProducts(limit);
        const additional = bestsellers
          .filter(p => p.id !== parseInt(currentProductId) && !similarProducts.find(s => s.id === p.id))
          .slice(0, limit - similarProducts.length);

        return [...similarProducts, ...additional];
      }

      return similarProducts;
    } catch (error) {
      console.error('API failed, falling back to static data:', error);
      // Fallback to static data logic
      const similar = staticProducts
        .filter(p => p.id !== parseInt(currentProductId) && p.category === category)
        .slice(0, limit);

      if (similar.length < limit) {
        const additional = staticProducts
          .filter(p => p.id !== parseInt(currentProductId) && !similar.find(s => s.id === p.id))
          .slice(0, limit - similar.length);

        return [...similar, ...additional];
      }

      return similar;
    }
  },

  /**
   * WISHLIST API METHODS
   */
  async getWishlist(userId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/wishlist`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch wishlist');
      }

      const data = await response.json();
      // Backend returns { data: { items: [...], total_count: ..., stats: ... } }
      // Frontend expects just the items array
      return data.data?.items || [];
    } catch (error) {
      console.error('Get wishlist failed:', error);
      throw error;
    }
  },

  async addToWishlist(userId, productId, notes = null, priority = 'medium') {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/wishlist/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          product_id: productId,
          notes: notes,
          priority: priority
        })
      });

      if (!response.ok) {
        throw new Error('Failed to add to wishlist');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Add to wishlist failed:', error);
      throw error;
    }
  },

  async removeFromWishlist(userId, itemId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/wishlist/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to remove from wishlist');
      }

      return true;
    } catch (error) {
      console.error('Remove from wishlist failed:', error);
      throw error;
    }
  },

  /**
   * CART API METHODS
   */
  async getCart(userId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cart`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch cart');
      }

      const data = await response.json();
      return data.data || { items: [], total_items: 0, subtotal: 0 };
    } catch (error) {
      console.error('Get cart failed:', error);
      throw error;
    }
  },

  async addToCart(userId, cartItem) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cart/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(cartItem)
      });

      if (!response.ok) {
        throw new Error('Failed to add to cart');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Add to cart failed:', error);
      throw error;
    }
  },

  async updateCartItem(userId, itemId, quantity) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cart/items/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({ quantity })
      });

      if (!response.ok) {
        throw new Error('Failed to update cart item');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Update cart item failed:', error);
      throw error;
    }
  },

  async removeFromCart(userId, itemId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cart/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to remove from cart');
      }

      return true;
    } catch (error) {
      console.error('Remove from cart failed:', error);
      throw error;
    }
  },

  async clearCart(userId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cart/clear`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to clear cart');
      }

      return true;
    } catch (error) {
      console.error('Clear cart failed:', error);
      throw error;
    }
  },

  /**
   * OUTFIT API METHODS
   */
  async getOutfits(userId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch outfits');
      }

      const data = await response.json();
      return data.data?.outfits || [];
    } catch (error) {
      console.error('Get outfits failed:', error);
      throw error;
    }
  },

  async createOutfit(userId, outfitData) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(outfitData)
      });

      if (!response.ok) {
        throw new Error('Failed to create outfit');
      }

      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {
      console.error('Create outfit failed:', error);
      throw error;
    }
  },

  async getOutfit(userId, outfitId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits/${outfitId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch outfit');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Get outfit failed:', error);
      throw error;
    }
  },

  async updateOutfit(userId, outfitId, outfitData) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits/${outfitId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(outfitData)
      });

      if (!response.ok) {
        throw new Error('Failed to update outfit');
      }

      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {
      console.error('Update outfit failed:', error);
      throw error;
    }
  },

  async deleteOutfit(userId, outfitId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits/${outfitId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete outfit');
      }

      return true;
    } catch (error) {
      console.error('Delete outfit failed:', error);
      throw error;
    }
  },

  async addItemToOutfit(userId, outfitId, itemData) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits/${outfitId}/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(itemData)
      });

      if (!response.ok) {
        throw new Error('Failed to add item to outfit');
      }

      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {
      console.error('Add item to outfit failed:', error);
      throw error;
    }
  },

  async removeItemFromOutfit(userId, outfitId, itemId) {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/outfits/${outfitId}/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to remove item from outfit');
      }

      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {
      console.error('Remove item from outfit failed:', error);
      throw error;
    }
  }
};

// Export configuration for easy toggling
export { USE_API };
